'use client';

import React, { useState, useEffect } from 'react';

interface PageSplashScreenProps {
  onComplete?: () => void;
  duration?: number;
  locale?: 'ar' | 'en';
  title?: string;
  subtitle?: string;
  showProgress?: boolean;
}

const PageSplashScreen: React.FC<PageSplashScreenProps> = ({
  onComplete,
  duration = 1500,
  locale = 'ar',
  title,
  subtitle,
  showProgress = false
}) => {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  const texts = {
    ar: {
      loading: 'جاري التحميل...',
      defaultTitle: 'دروب هاجر',
      defaultSubtitle: 'معدات الضيافة والمطاعم'
    },
    en: {
      loading: 'Loading...',
      defaultTitle: 'DROOB HAJER',
      defaultSubtitle: 'Hospitality & Restaurant Equipment'
    }
  };

  const t = texts[locale];
  const displayTitle = title || t.defaultTitle;
  const displaySubtitle = subtitle || t.defaultSubtitle;

  useEffect(() => {
    if (showProgress) {
      const interval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            setTimeout(() => {
              setIsVisible(false);
              setTimeout(() => {
                onComplete?.();
              }, 300);
            }, 200);
            return 100;
          }
          return prev + 4;
        });
      }, duration / 25);

      return () => clearInterval(interval);
    } else {
      const timer = setTimeout(() => {
        setIsVisible(false);
        setTimeout(() => {
          onComplete?.();
        }, 300);
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration, onComplete, showProgress]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-[9998] flex items-center justify-center bg-gradient-to-br from-primary/95 via-blue-600/95 to-blue-800/95 backdrop-blur-sm">
      {/* Subtle Background Animation */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-1/3 left-1/3 w-32 h-32 bg-white rounded-full animate-pulse"></div>
        <div className="absolute bottom-1/3 right-1/3 w-24 h-24 bg-white rounded-full animate-pulse delay-500"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center px-8 max-w-md mx-auto">
        {/* Compact Logo */}
        <div className="relative mb-6">
          <div className="relative w-20 h-20 mx-auto bg-white rounded-full shadow-xl flex items-center justify-center">
            <div className="w-16 h-16 bg-gradient-to-br from-primary to-blue-600 rounded-full flex items-center justify-center">
              <span className="text-white text-2xl font-bold font-tajawal">د</span>
            </div>
          </div>
          <div className="absolute inset-0 border-2 border-white border-opacity-30 rounded-full animate-spin-slow"></div>
        </div>

        {/* Page Title */}
        <div className="space-y-2 mb-6">
          <h1 className="text-white text-xl md:text-2xl font-bold font-tajawal animate-fade-in-up">
            {displayTitle}
          </h1>
          <p className="text-white/80 text-sm md:text-base font-medium animate-fade-in-up delay-300">
            {displaySubtitle}
          </p>
        </div>

        {/* Progress Section */}
        {showProgress && (
          <div className="space-y-3 animate-fade-in-up delay-500">
            <div className="w-full bg-white/20 rounded-full h-1 overflow-hidden">
              <div 
                className="h-full bg-white rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              />
            </div>
            <p className="text-white/60 text-xs">
              {Math.round(progress)}%
            </p>
          </div>
        )}

        {/* Loading Indicator */}
        {!showProgress && (
          <div className="flex items-center justify-center space-x-1 rtl:space-x-reverse animate-fade-in-up delay-500">
            <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce"></div>
            <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce delay-100"></div>
            <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce delay-200"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PageSplashScreen;
