'use client';

import React, { useState, useEffect } from 'react';

interface MobileSplashScreenProps {
  onComplete?: () => void;
  duration?: number;
  locale?: 'ar' | 'en';
}

const MobileSplashScreen: React.FC<MobileSplashScreenProps> = ({
  onComplete,
  duration = 2500,
  locale = 'ar'
}) => {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [currentStep, setCurrentStep] = useState(0);

  const texts = {
    ar: {
      welcome: 'مرحباً بك في',
      brandName: 'دروب هاجر',
      tagline: 'معدات الضيافة والمطاعم',
      loading: 'جاري التحميل...',
      steps: ['تحميل البيانات...', 'إعداد التطبيق...', 'تجهيز المحتوى...', 'تم التحميل!']
    },
    en: {
      welcome: 'Welcome to',
      brandName: 'DROOB HAJER',
      tagline: 'Hospitality & Restaurant Equipment',
      loading: 'Loading...',
      steps: ['Loading data...', 'Setting up app...', 'Preparing content...', 'Ready!']
    }
  };

  const t = texts[locale];

  useEffect(() => {
    const progressInterval = duration / 100;

    const progressTimer = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + 1;
        
        // Update step based on progress
        const step = Math.floor(newProgress / 25);
        setCurrentStep(Math.min(step, 3));
        
        if (newProgress >= 100) {
          clearInterval(progressTimer);
          setTimeout(() => {
            setIsVisible(false);
            setTimeout(() => {
              onComplete?.();
            }, 300);
          }, 200);
          return 100;
        }
        return newProgress;
      });
    }, progressInterval);

    return () => clearInterval(progressTimer);
  }, [duration, onComplete]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex flex-col items-center justify-center bg-gradient-to-br from-primary via-blue-600 to-blue-800 overflow-hidden">
      {/* Mobile Background Effects */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-white rounded-full animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-48 h-48 bg-white rounded-full animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 right-1/3 w-24 h-24 bg-white rounded-full animate-pulse delay-500"></div>
      </div>

      {/* Floating Particles - Reduced for mobile */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1.5 h-1.5 bg-white rounded-full opacity-40 animate-splash-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center px-6 max-w-sm mx-auto">
        {/* Mobile Logo Container */}
        <div className="relative mb-6">
          {/* Logo Background Glow */}
          <div className="absolute inset-0 bg-white rounded-full opacity-30 animate-ping"></div>
          
          {/* Logo */}
          <div className="relative w-24 h-24 mx-auto bg-white rounded-full shadow-2xl flex items-center justify-center transform transition-all duration-1000">
            <div className="w-18 h-18 bg-gradient-to-br from-primary to-blue-600 rounded-full flex items-center justify-center shadow-inner">
              <span className="text-white text-2xl font-bold font-tajawal">د</span>
            </div>
          </div>

          {/* Logo Animation Ring */}
          <div className="absolute inset-0 border-2 border-white border-opacity-40 rounded-full animate-splash-spin-slow"></div>
        </div>

        {/* Brand Text - Mobile Optimized */}
        <div className="space-y-3 mb-6">
          <div className="transform transition-all duration-1000 delay-300">
            <p className="text-white/80 text-base font-medium mb-1 animate-splash-fade-in-up">
              {t.welcome}
            </p>
            <h1 className="text-white text-2xl font-bold font-tajawal mb-1 animate-splash-fade-in-up splash-delay-500">
              {t.brandName}
            </h1>
            <p className="text-white/90 text-sm font-medium animate-splash-fade-in-up splash-delay-700">
              {t.tagline}
            </p>
          </div>
        </div>

        {/* Mobile Progress Section */}
        <div className="space-y-3 animate-splash-fade-in-up splash-delay-1000">
          {/* Progress Bar */}
          <div className="w-full bg-white/20 rounded-full h-1.5 overflow-hidden backdrop-blur-sm">
            <div 
              className="h-full bg-gradient-to-r from-white to-blue-200 rounded-full transition-all duration-300 ease-out shadow-lg"
              style={{ width: `${progress}%` }}
            />
          </div>
          
          {/* Current Step */}
          <div className="min-h-[20px]">
            <p className="text-white/80 text-xs font-medium">
              {t.steps[currentStep]}
            </p>
          </div>
          
          {/* Progress Dots */}
          <div className="flex items-center justify-center space-x-1 rtl:space-x-reverse">
            <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce"></div>
            <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce splash-delay-100"></div>
            <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce splash-delay-200"></div>
          </div>
          
          {/* Progress Percentage */}
          <p className="text-white/50 text-xs font-medium">
            {Math.round(progress)}%
          </p>
        </div>
      </div>

      {/* Mobile Bottom Safe Area */}
      <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-black/30 to-transparent safe-area-pb"></div>
      
      {/* Mobile Top Safe Area */}
      <div className="absolute top-0 left-0 right-0 h-12 bg-gradient-to-b from-black/20 to-transparent safe-area-pt"></div>
    </div>
  );
};

export default MobileSplashScreen;
